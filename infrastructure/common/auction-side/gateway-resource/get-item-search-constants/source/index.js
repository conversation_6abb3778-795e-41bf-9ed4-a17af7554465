const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
// Use main proxy endpoint instead of read-only until read replicas are enabled
// const pool = new PgPool(process.env.PGHOST)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = (e, ctx, cb) => {
  const params = e.body
  const header = e.headers
  // const authorizer = e.authorizer;
  const base = new Base(pool, params.languageCode)
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      const sqlParams = [
        tenant.tenant_no,
        ['PRODUCT_CATEGORY', 'TAX_RATE', 'PITCH_FOLLOW_BID_PRICE'],
        base.language,
      ]
      return pool.query(
        'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
        sqlParams
      )
    })
    .then(data => {
      return base.createSuccessResponse(cb, data)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
