variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "profile_name" {
  description = "Profile name"
}

variable "external_domain_name" {
  description = "external_domain_name"
}

variable "auction_domain_name" {
  description = "domain_name"
}

variable "admin_domain_name" {
  description = "domain_name"
}

variable "web_socket_domain" {
  description = "domain_name"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-bucket" {
  description = "File upload bucket bucket"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "bucket_regional_domain_name" {
  description = "File upload bucket bucket_regional_domain_name"
}

variable "api_gateway_5xx_alarm_slack_hook_url" {
  description = "api_gateway_5xx_alarm_slack_hook_url"
  default = ""
}

variable "serverless_min_capacity" {
  description = "Minimum capacity for Aurora Serverless v2"
  type        = number
  default     = 0
}

variable "serverless_max_capacity" {
  description = "Maximum capacity for Aurora Serverless v2"
  type        = number
  default     = 16.0
}

variable "nat_gateway_amount" {
  description = "nat_gateway_amount(1 or 2)"
  default = 2
}

variable "admin_lambda_global_environment_variables" {
  description = "admin_lambda_global_environment_variables"
  default     = {}
}

variable "auction_lambda_global_environment_variables" {
  description = "auction_lambda_global_environment_variables"
  default     = {}
}

variable "batch-cron-rule" {
  description = "batch-cron-rule"
}

variable "record_name" {
  description = "record_name"
  default = ""
}

variable "mail_from_domain" {
  description = "mail_from_domain"
}

variable "basic_auth_enable" {
  description = "basic_auth_enable"
  default = true
}

variable "admin_basic_auth_enable" {
  description = "admin_basic_auth_enable"
}

variable "password_policy" {
  description = "Password policy for the user pool"
  type = object({
    minimum_length                   = number
    require_lowercase                = bool
    require_numbers                  = bool
    require_symbols                  = bool
    require_uppercase                = bool
    temporary_password_validity_days = number
  })
  default = {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = false
    require_uppercase                = true
    temporary_password_validity_days = 7
  }
}

variable "admin_tenants" {
  description = "admin_tenants"
  type = map(object({
    tenant_id        = string
    use_custom_domain = bool
    domain_name       = string
    hosted_zone_name  = string
  }))
}

variable "admin_custom_domains" {
  description = "admin_custom_domains"
  type = list(string)
}

variable "admin_acm_domain_name" {
  description = "admin_acm_domain_name"
  type = string
}

variable "auction_tenants" {
  description = "auction_tenants"
  type = map(object({
    tenant_id        = string
    use_custom_domain = bool
    domain_name       = string
    hosted_zone_name  = string
  }))
  default = {}
}

variable "auction_custom_domains" {
  description = "auction_custom_domains"
  type = list(string)
  default = []
}

variable "auction_acm_domain_name" {
  description = "auction_acm_domain_name"
  type = string
  default = ""
}

variable "admin_deployment_strategy" {
  description = "Admin-side deployment strategy: 'auto' for automatic deployment with triggers, 'manual' for manual deployment without triggers"
  type        = string
  default     = "auto"
  validation {
    condition     = contains(["auto", "manual"], var.admin_deployment_strategy)
    error_message = "Admin deployment strategy must be either 'auto' or 'manual'."
  }
}

variable "auction_deployment_strategy" {
  description = "Auction-side deployment strategy: 'auto' for automatic deployment with triggers, 'manual' for manual deployment without triggers"
  type        = string
  default     = "auto"
  validation {
    condition     = contains(["auto", "manual"], var.auction_deployment_strategy)
    error_message = "Auction deployment strategy must be either 'auto' or 'manual'."
  }
}

variable "enable_read_replica" {
  description = "Enable read replica instance for better read performance"
  type        = bool
  default     = false
}
