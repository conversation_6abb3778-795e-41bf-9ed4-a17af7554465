const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);

const {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider');

const cognitoClient = new CognitoIdentityProviderClient({});
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  console.log('Received registration request:', e);
  ctx.callbackWaitsForEmptyEventLoop = false;

  // Ensure we always have proper error handling with CORS headers
  try {
    console.log('Raw request body:', e.body);
    const params = Base.parseRequestBody(e.body);
    console.log('Parsed params:', params);

    // Validate required parameters
    if (!params || typeof params !== 'object') {
      throw new Error('Invalid request body format');
    }

    const tenantId = params.tenantId ? parseInt(params.tenantId) : 1;
    console.log('params.tenantId : ', tenantId);

  let createdUserId = null;

  // Rollback function to delete the user from database if Cognito creation fails
  const rollback = () => {
    // Return a Promise to properly chain
    return pool
      .rlsQuery(Base.extractTenantId(e),
        "SELECT * FROM t_member_request WHERE tenant_no = $1 AND free_field->>'email' = $2;",
        [tenantId, params.email]
      )
      .then(exists => {
        if (exists && exists.length > 0) {
          return pool
            .rlsQuery(Base.extractTenantId(e),
              "DELETE FROM t_member_request WHERE tenant_no = $1 AND free_field->>'email' = $2;",
              [tenantId, params.email]
            )
            .then(() => {
              console.log('Rolled back user: ', params.email);
              // Explicitly reject to break the chain
              return Promise.reject(
                new Error('Registration failed - database rolled back')
              );
            });
        }
        console.log('No user found to roll back for: ', params.email);
        return Promise.reject(new Error('Registration failed'));
      });
  };

  Promise.resolve()
    .then(() => Base.startRequest(e))
    // .then(() => Validator.validation(params))
    .then(() => {
      return pool.rlsQuery(Base.extractTenantId(e),
        "SELECT * FROM t_member_request WHERE tenant_no = $1 AND free_field->>'email' = $2;",
        [tenantId, params.email]
      );
    })
    .then(memberRequest => {
      if (memberRequest && memberRequest.length > 0) {
        const error = new Error('Email already exists in the database');
        error.code = 'EMAIL_EXISTS';
        throw error;
      }

      return Base.hashPassword(params.password).then(hash => {
        const freeField = {
          email: params.email,
          password: hash,
          role: 'system_admin',
          lang: params.languageCode || 'ja',
          admin_name: params.username,
          role_id: params.role,
          tenant_id: tenantId,
        };
        return pool
          .rlsQuery(Base.extractTenantId(e),'SELECT * FROM "f_insert_member_request"($1,$2,$3);', [
            tenantId,
            1,
            freeField,
          ])
          .then(dbResult => {
            createdUserId = dbResult?.[0].member_request_no || '';
            return dbResult;
          });
      });
    })
    .then(() => {
      // Create the user in Cognito with updated custom attributes
      const command = new AdminCreateUserCommand({
        UserPoolId: process.env.COGNITO_USER_POOL_ID,
        Username: params.email,
        UserAttributes: [
          {Name: 'email', Value: params.email},
          {Name: 'email_verified', Value: 'true'},
          // TODO: admin_noは固定Noの代わりにシーケンス番号を使用する
          {Name: 'custom:admin_no', Value: '1'},
          {
            Name: 'custom:admin_language_code',
            Value: params.languageCode || 'ja',
          },
          {Name: 'custom:role_id', Value: params.role},
          {Name: 'custom:admin_name', Value: params.username},
        ],
        TemporaryPassword: params.password,
        MessageAction: 'SUPPRESS',
      });
      return cognitoClient
        .send(command)
        .then(createUserResult => {
          console.log('User created in Cognito:', createUserResult);
          return {createUserResult, email: params.email};
        })
        .catch(error => {
          // If Cognito creation fails, roll back the database insert
          console.error('Failed to create user in Cognito:', error);
          if (createdUserId) {
            return rollback().catch(rollbackError => {
              console.error(
                'Failed to roll back database changes:',
                rollbackError
              );
              // Always reject with the original error
              return Promise.reject(error);
            });
          }
          return Promise.reject(error);
        });
    })
    .then(({createUserResult, email}) => {
      const username = createUserResult.User.Username;

      // TODO
      // Set permanent password
      const setPasswordCommand = new AdminSetUserPasswordCommand({
        UserPoolId: process.env.COGNITO_USER_POOL_ID,
        Username: username,
        Password: params.password,
        Permanent: true,
      });

      return cognitoClient.send(setPasswordCommand).then(() => {
        console.log('Password set to permanent for user:', username);
        return {username, createUserResult, email};
      });
    })
    .then(({username, createUserResult, email}) => {
      // Add user to the specified group (tenant)
      const group = `${Common.getCognitoGroupPrefix()}${tenantId}`;
      const groupCommand = new AdminAddUserToGroupCommand({
        UserPoolId: process.env.COGNITO_USER_POOL_ID,
        Username: username,
        GroupName: group,
      });

      return cognitoClient.send(groupCommand).then(() => {
        console.log(`User ${username} added to group ${group}`);
        return {
          message: 'User registered successfully',
          username: username,
          userSub: createUserResult.User.Username,
          group: group,
          userId: createdUserId,
          email: email,
          tenantId: tenantId,
          role: params.role,
          adminName: params.username,
        };
      });
    })
    .then(response => Base.createSuccessResponse(cb, response))
    .catch(error => {
      console.error('Error during registration:', error);
      let response;

      // Handle specific AWS Cognito errors
      if (error.name === 'UsernameExistsException') {
        response = {
          status: 400,
          error: 'UsernameExistsException',
          message: 'User account already exists',
        };
      } else if (
        error.name === 'InvalidPasswordException' ||
        error.__type === 'InvalidPasswordException'
      ) {
        response = {
          status: 400,
          error: 'InvalidPasswordException',
          message:
            error.message ||
            'Password must include uppercase, lowercase, numbers, and special characters',
        };
      } else if (error.name === 'InvalidParameterException') {
        response = {
          status: 400,
          error: 'InvalidParameterException',
          message: 'Invalid parameters provided',
        };
      } else if (error.code === 'EMAIL_EXISTS') {
        response = {
          status: 400,
          error: 'EmailExistsException',
          message: 'Email already exists in the database',
        };
      } else if (error.status) {
        // Handle errors that already have a status code
        response = {
          status: error.status,
          error: error.error || error.name || 'Error',
          message: error.message || 'An error occurred',
        };
      } else {
        // Handle unexpected errors
        response = {
          status: 500,
          error: error.name || 'InternalServerError',
          message: error.message || 'An unexpected error occurred',
        };
      }

      return Base.createErrorResponse(cb, response);
    });
  } catch (parseError) {
    console.error('Error parsing request or initializing:', parseError);
    const errorResponse = {
      status: 400,
      error: 'BadRequest',
      message: 'Invalid request format',
    };
    return Base.createErrorResponse(cb, errorResponse);
  }
};
